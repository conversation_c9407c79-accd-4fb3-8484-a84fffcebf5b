// import {Track } from "livekit-client";
// import { NoiseSuppressionProcessor } from "@shiguredo/noise-suppression";

// const noiseProcessor = useRef(null);

// noiseProcessor.current = new NoiseSuppressionProcessor();
// const processedTrack = await noiseProcessor.current?.startProcessing(
//               localAudioTrack.mediaStreamTrack,
//             );
// if (processedTrack) {
//    await localAudioTrack.replaceTrack(processedTrack, true);
// }



exports.enableNoiseSuppression = async (localAudioTrack, noiseProcessorRef) => {
    console.log("Noise suppresion ",noiseProcessorRef.current.startProcessing);
    console.log("Noise suppresion ",localAudioTrack.mediaStreamTrack);

    try {
        // Check if the track is valid and not ended
        const mediaStreamTrack = localAudioTrack.mediaStreamTrack;
        if (!mediaStreamTrack || mediaStreamTrack.readyState === 'ended') {
            console.log("Audio track is not available or has ended, skipping noise suppression");
            return;
        }

        // Check if noise suppression is already processing
        if (noiseProcessorRef.current.isProcessing && noiseProcessorRef.current.isProcessing()) {
            console.log("Noise suppression is already processing, stopping first");
            noiseProcessorRef.current.stopProcessing();
        }

        const processedTrack = await noiseProcessorRef.current.startProcessing(mediaStreamTrack);
        if (processedTrack) {
          await localAudioTrack.replaceTrack(processedTrack, true);
        }
    } catch (error) {
        console.log("Noise suppresion error:",error);
        // If there's an error, make sure to clean up the processor
        if (noiseProcessorRef.current && noiseProcessorRef.current.stopProcessing) {
            try {
                noiseProcessorRef.current.stopProcessing();
            } catch (cleanupError) {
                console.log("Error during cleanup:", cleanupError);
            }
        }
    }
};

exports.disableNoiseSuppression = async (localAudioTrack, noiseProcessorRef) => {
  try {
    if (noiseProcessorRef.current) {
      // Stop the noise processing
      if (noiseProcessorRef.current.stopProcessing) {
        noiseProcessorRef.current.stopProcessing();
      }
      noiseProcessorRef.current = null;
    }

    // Optionally restart the original track if needed
    // This helps ensure the track is in a clean state
    if (localAudioTrack && localAudioTrack.mediaStreamTrack) {
      console.log("Noise suppression disabled, track should return to original state");
    }
  } catch (error) {
    console.log("Error disabling noise suppression:", error);
    // Ensure processor is cleaned up even if there's an error
    noiseProcessorRef.current = null;
  }
};